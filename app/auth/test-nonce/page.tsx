"use client";

import { useState } from "react";
import { useTurnkeyNonce } from "@/hooks/useTurnkeyNonce";
import { signIn } from "next-auth/react";

export default function TestNoncePage() {
  const { getNonce } = useTurnkeyNonce();
  const [nonce, setNonce] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const generateNonce = async () => {
    try {
      setLoading(true);
      const generatedNonce = await getNonce();
      setNonce(generatedNonce);
      console.log("Generated Turnkey nonce:", generatedNonce);
    } catch (error) {
      console.error("Error generating nonce:", error);
    } finally {
      setLoading(false);
    }
  };

  const testGoogleLogin = async () => {
    try {
      setLoading(true);
      
      // Generate and store nonce
      const generatedNonce = await getNonce();
      document.cookie = `turnkey_login_nonce=${generatedNonce}; Path=/; SameSite=Lax; Secure`;
      console.log("Set nonce cookie:", generatedNonce);
      
      // Trigger NextAuth signIn
      await signIn("google");
    } catch (error) {
      console.error("Error during Google login:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-6">Test Nonce Integration</h1>
      
      <div className="space-y-4">
        <button
          onClick={generateNonce}
          disabled={loading}
          className="w-full bg-blue-500 text-white p-2 rounded disabled:opacity-50"
        >
          {loading ? "Generating..." : "Generate Turnkey Nonce"}
        </button>
        
        {nonce && (
          <div className="p-3 bg-gray-100 rounded">
            <p className="text-sm font-mono break-all">
              <strong>Nonce:</strong> {nonce}
            </p>
          </div>
        )}
        
        <button
          onClick={testGoogleLogin}
          disabled={loading}
          className="w-full bg-green-500 text-white p-2 rounded disabled:opacity-50"
        >
          {loading ? "Signing in..." : "Test Google Login with Nonce"}
        </button>
      </div>
      
      <div className="mt-6 text-sm text-gray-600">
        <p>This page helps test the nonce integration between Turnkey and NextAuth.</p>
        <p className="mt-2">Check the browser console for nonce values and validation logs.</p>
      </div>
    </div>
  );
}
