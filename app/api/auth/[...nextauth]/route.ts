import NextAuth from "next-auth";
import { handleCallback<PERSON>rovider, handleLoginSuccess } from "../_helpers";
import { cookies } from "next/headers";
import { jwtDecode } from "jwt-decode";

const authOptions = {
  session: { strategy: "jwt" as const },
  providers: [
    {
      id: "google",
      name: "Google",
      type: "oidc",
      clientId: process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      wellKnown: "https://accounts.google.com/.well-known/openid_configuration",
      authorization: {
        async params(request: any) {
          // Get the custom nonce from cookie if available
          const cookieHeader = request.headers?.cookie || "";
          const nonceCookie = cookieHeader
            .split(";")
            .find((c: string) => c.trim().startsWith("turnkey_login_nonce="));
          const customNonce = nonceCookie?.split("=")[1]?.trim();

          return {
            scope: "openid email profile",
            response_type: "code",
            ...(customNonce && { nonce: customNonce }),
          };
        },
      },
      checks: ["pkce", "state", "nonce"],
      profile(profile: any) {
        return {
          id: profile.sub,
          name: profile.name,
          email: profile.email,
          image: profile.picture,
        };
      },
    },
  ],
  callbacks: {
    async signIn({ account, profile, user }: any) {
      console.log(account, "account");
      const accessOrIdToken = account?.id_token;
      if (!accessOrIdToken) {
        return `/auth/error?error=NoAccessToken`;
      }

      // NextAuth has already validated the nonce automatically with OIDC provider
      // Log the nonce for debugging purposes
      try {
        const decoded: any = jwtDecode(accessOrIdToken);
        const idTokenNonce: string | undefined = decoded?.nonce;
        console.log(
          { decoded, idTokenNonce, accessOrIdToken },
          "NextAuth validated nonce successfully"
        );
      } catch (e) {
        console.error("Error decoding ID token:", e);
        return `/auth/error?error=InvalidIdToken`;
      }

      try {
        const provider = account?.provider || "google";
        const email = profile?.email || user?.email || null;

        const result: any = await handleCallbackProvider(
          accessOrIdToken,
          provider,
          email
        );
        await handleLoginSuccess(result?.jwtToken);

        // Clear nonce cookie after successful validation
        try {
          const responseCookies = await cookies();
          responseCookies.set("turnkey_login_nonce", "", {
            httpOnly: false,
            secure: true,
            sameSite: "lax",
            path: "/",
            expires: new Date(0),
          } as any);
        } catch {}

        return true;
      } catch (error: any) {
        console.log(error, "error");
        return `/auth/error?error=${encodeURIComponent(
          error?.message || "BackendAuthFailed"
        )}`;
      }
    },
    async jwt({ token, account }: any) {
      if (account?.id_token) token.googleIdToken = account.id_token;
      return token;
    },
    async session({ session, token }: any) {
      (session.user as any) = session.user || {};
      (session.user as any).googleIdToken = token.googleIdToken;
      return session;
    },
    async redirect({ url, baseUrl }: any) {
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
};

const handler = NextAuth(authOptions as any);
export { handler as GET, handler as POST };
